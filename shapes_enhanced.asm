section .data
    ; Program title and menu
    msg_title       db '=== Enhanced Shape Drawing Program ===', 10, 0
    msg_menu        db 10, 'Select a shape to draw:', 10
                    db '1. Rectangle', 10
                    db '2. Triangle', 10
                    db '3. Circle', 10
                    db '4. Square', 10
                    db '5. Exit', 10
                    db 'Enter your choice (1-5): ', 0

    ; Rectangle messages
    msg_rect_title  db 10, '--- Rectangle Drawing ---', 10, 0
    msg_width       db 'Enter width (1-80): ', 0
    msg_height      db 'Enter height (1-25): ', 0
    msg_rect_style  db 'Choose style (1=Filled *, 2=Border #, 3=Dotted .): ', 0
    msg_rect_error  db 'Value out of range! Width: 1-80, Height: 1-25.', 10, 0

    ; Triangle messages
    msg_tri_title   db 10, '--- Triangle Drawing ---', 10, 0
    msg_tri_size    db 'Enter triangle size (1-25): ', 0
    msg_tri_style   db 'Choose style (1=Right *, 2=Centered *, 3=Hollow #): ', 0
    msg_tri_error   db 'Value out of range! Size must be 1-25.', 10, 0

    ; Circle messages
    msg_circ_title  db 10, '--- Circle Drawing ---', 10, 0
    msg_circ_info   db 'Drawing a fixed-size circle...', 10, 0

    ; Square messages
    msg_square_title db 10, '--- Square Drawing ---', 10, 0
    msg_square_size  db 'Enter square size (1-25): ', 0
    msg_square_style db 'Choose style (1=Filled *, 2=Border #, 3=Dotted .): ', 0
    msg_square_error db 'Value out of range! Size must be 1-25.', 10, 0

    ; Error messages
    msg_error_input db 'Invalid input! Please enter a number.', 10, 0
    msg_error_choice db 'Invalid choice! Please enter 1-5.', 10, 0
    msg_style_error  db 'Invalid style! Please enter 1, 2, or 3.', 10, 0
    msg_max_attempts db 'Too many invalid attempts. Returning to menu.', 10, 0
    msg_goodbye     db 10, 'Thank you for using Enhanced Shape Drawing Program!', 10, 0

    ; Drawing characters and colors
    star            db '*'
    hash            db '#'
    dot             db '.'
    space           db ' '
    newline         db 10

    ; ANSI Color codes
    color_red       db 27, '[31m', 0
    color_green     db 27, '[32m', 0
    color_yellow    db 27, '[33m', 0
    color_blue      db 27, '[34m', 0
    color_magenta   db 27, '[35m', 0
    color_cyan      db 27, '[36m', 0
    color_reset     db 27, '[0m', 0

    ; Constants
    max_attempts    equ 3

section .bss
    choice          resd 1
    width           resd 1
    height          resd 1
    size            resd 1
    style           resd 1
    input           resb 16
    attempts        resd 1

section .text
    global _start

_start:
    ; Show program title with color
    mov edi, color_blue
    call print_string
    mov edi, msg_title
    call print_string
    mov edi, color_reset
    call print_string

main_loop:
    ; Display main menu
    mov edi, msg_menu
    call print_string

    ; Read user choice
    call read_int
    cmp eax, -1
    je .invalid_choice
    cmp eax, 1
    jl .invalid_choice
    cmp eax, 5
    jg .invalid_choice

    mov [choice], eax

    ; Jump to appropriate handler
    cmp eax, 1
    je draw_rectangle_menu
    cmp eax, 2
    je draw_triangle_menu
    cmp eax, 3
    je draw_circle_menu
    cmp eax, 4
    je draw_square_menu
    cmp eax, 5
    je exit_program

.invalid_choice:
    mov edi, color_red
    call print_string
    mov edi, msg_error_choice
    call print_string
    mov edi, color_reset
    call print_string
    jmp main_loop

; Rectangle drawing menu
draw_rectangle_menu:
    mov edi, color_green
    call print_string
    mov edi, msg_rect_title
    call print_string
    mov edi, color_reset
    call print_string

    ; Get width
    mov dword [attempts], 0
.get_width:
    cmp dword [attempts], max_attempts
    jge .max_attempts_reached

    mov edi, msg_width
    call print_string
    call read_int

    cmp eax, -1
    je .invalid_input_width
    cmp eax, 1
    jl .invalid_range_width
    cmp eax, 80
    jg .invalid_range_width

    mov [width], eax
    jmp .get_height

.invalid_input_width:
    mov edi, color_red
    call print_string
    mov edi, msg_error_input
    call print_string
    mov edi, color_reset
    call print_string
    inc dword [attempts]
    jmp .get_width

.invalid_range_width:
    mov edi, color_red
    call print_string
    mov edi, msg_rect_error
    call print_string
    mov edi, color_reset
    call print_string
    inc dword [attempts]
    jmp .get_width

.get_height:
    mov dword [attempts], 0
.get_height_loop:
    cmp dword [attempts], max_attempts
    jge .max_attempts_reached

    mov edi, msg_height
    call print_string
    call read_int

    cmp eax, -1
    je .invalid_input_height
    cmp eax, 1
    jl .invalid_range_height
    cmp eax, 25
    jg .invalid_range_height

    mov [height], eax
    jmp .get_style

.invalid_input_height:
    mov edi, color_red
    call print_string
    mov edi, msg_error_input
    call print_string
    mov edi, color_reset
    call print_string
    inc dword [attempts]
    jmp .get_height_loop

.invalid_range_height:
    mov edi, color_red
    call print_string
    mov edi, msg_rect_error
    call print_string
    mov edi, color_reset
    call print_string
    inc dword [attempts]
    jmp .get_height_loop

.get_style:
    mov dword [attempts], 0
.get_style_loop:
    cmp dword [attempts], max_attempts
    jge .max_attempts_reached

    mov edi, msg_rect_style
    call print_string
    call read_int

    cmp eax, -1
    je .invalid_input_style
    cmp eax, 1
    jl .invalid_range_style
    cmp eax, 3
    jg .invalid_range_style

    mov [style], eax
    call draw_rectangle
    jmp main_loop

.invalid_input_style:
    mov edi, color_red
    call print_string
    mov edi, msg_error_input
    call print_string
    mov edi, color_reset
    call print_string
    inc dword [attempts]
    jmp .get_style_loop

.invalid_range_style:
    mov edi, color_red
    call print_string
    mov edi, msg_style_error
    call print_string
    mov edi, color_reset
    call print_string
    inc dword [attempts]
    jmp .get_style_loop

.max_attempts_reached:
    mov edi, color_yellow
    call print_string
    mov edi, msg_max_attempts
    call print_string
    mov edi, color_reset
    call print_string
    jmp main_loop

; Triangle drawing menu
draw_triangle_menu:
    mov edi, color_magenta
    call print_string
    mov edi, msg_tri_title
    call print_string
    mov edi, color_reset
    call print_string

    mov dword [attempts], 0
.get_size:
    cmp dword [attempts], max_attempts
    jge .max_attempts_reached

    mov edi, msg_tri_size
    call print_string
    call read_int

    cmp eax, -1
    je .invalid_input_size
    cmp eax, 1
    jl .invalid_range_size
    cmp eax, 25
    jg .invalid_range_size

    mov [size], eax
    jmp .get_style

.invalid_input_size:
    mov edi, color_red
    call print_string
    mov edi, msg_error_input
    call print_string
    mov edi, color_reset
    call print_string
    inc dword [attempts]
    jmp .get_size

.invalid_range_size:
    mov edi, color_red
    call print_string
    mov edi, msg_tri_error
    call print_string
    mov edi, color_reset
    call print_string
    inc dword [attempts]
    jmp .get_size

.get_style:
    mov dword [attempts], 0
.get_style_loop:
    cmp dword [attempts], max_attempts
    jge .max_attempts_reached

    mov edi, msg_tri_style
    call print_string
    call read_int

    cmp eax, -1
    je .invalid_input_style
    cmp eax, 1
    jl .invalid_range_style
    cmp eax, 3
    jg .invalid_range_style

    mov [style], eax
    call draw_triangle
    jmp main_loop

.invalid_input_style:
    mov edi, color_red
    call print_string
    mov edi, msg_error_input
    call print_string
    mov edi, color_reset
    call print_string
    inc dword [attempts]
    jmp .get_style_loop

.invalid_range_style:
    mov edi, color_red
    call print_string
    mov edi, msg_style_error
    call print_string
    mov edi, color_reset
    call print_string
    inc dword [attempts]
    jmp .get_style_loop

.max_attempts_reached:
    mov edi, color_yellow
    call print_string
    mov edi, msg_max_attempts
    call print_string
    mov edi, color_reset
    call print_string
    jmp main_loop

; Circle drawing menu
draw_circle_menu:
    mov edi, color_cyan
    call print_string
    mov edi, msg_circ_title
    call print_string
    mov edi, msg_circ_info
    call print_string
    mov edi, color_reset
    call print_string
    call draw_circle
    jmp main_loop

; Square drawing menu
draw_square_menu:
    mov edi, color_blue
    call print_string
    mov edi, msg_square_title
    call print_string
    mov edi, color_reset
    call print_string

    mov dword [attempts], 0
.get_size:
    cmp dword [attempts], max_attempts
    jge .max_attempts_reached

    mov edi, msg_square_size
    call print_string
    call read_int

    cmp eax, -1
    je .invalid_input_size
    cmp eax, 1
    jl .invalid_range_size
    cmp eax, 25
    jg .invalid_range_size

    mov [size], eax
    jmp .get_style

.invalid_input_size:
    mov edi, color_red
    call print_string
    mov edi, msg_error_input
    call print_string
    mov edi, color_reset
    call print_string
    inc dword [attempts]
    jmp .get_size

.invalid_range_size:
    mov edi, color_red
    call print_string
    mov edi, msg_square_error
    call print_string
    mov edi, color_reset
    call print_string
    inc dword [attempts]
    jmp .get_size

.get_style:
    mov dword [attempts], 0
.get_style_loop:
    cmp dword [attempts], max_attempts
    jge .max_attempts_reached

    mov edi, msg_square_style
    call print_string
    call read_int

    cmp eax, -1
    je .invalid_input_style
    cmp eax, 1
    jl .invalid_range_style
    cmp eax, 3
    jg .invalid_range_style

    mov [style], eax
    call draw_square
    jmp main_loop

.invalid_input_style:
    mov edi, color_red
    call print_string
    mov edi, msg_error_input
    call print_string
    mov edi, color_reset
    call print_string
    inc dword [attempts]
    jmp .get_style_loop

.invalid_range_style:
    mov edi, color_red
    call print_string
    mov edi, msg_style_error
    call print_string
    mov edi, color_reset
    call print_string
    inc dword [attempts]
    jmp .get_style_loop

.max_attempts_reached:
    mov edi, color_yellow
    call print_string
    mov edi, msg_max_attempts
    call print_string
    mov edi, color_reset
    call print_string
    jmp main_loop

; --- Drawing functions ---

; Draw rectangle with different styles
draw_rectangle:
    push ebx
    push ecx
    push edx
    push esi

    mov edi, color_green
    call print_string

    mov esi, 1                              ; Row counter
    mov ecx, [height]
.row_loop:
    push ecx
    push esi
    mov ecx, [width]
    mov edi, 1                              ; Column counter

.col_loop:
    push ecx
    push edi

    ; Choose character based on style
    mov eax, [style]
    cmp eax, 1
    je .draw_filled
    cmp eax, 2
    je .draw_border
    cmp eax, 3
    je .draw_dotted
    jmp .draw_filled

.draw_filled:
    mov eax, 4
    mov ebx, 1
    mov ecx, star
    mov edx, 1
    int 0x80
    jmp .continue_col

.draw_border:
    ; Check if on border
    cmp esi, 1                              ; First row?
    je .print_border_char
    cmp esi, [height]                       ; Last row?
    je .print_border_char
    cmp edi, 1                              ; First column?
    je .print_border_char
    cmp edi, [width]                        ; Last column?
    je .print_border_char

    ; Interior - print space
    mov eax, 4
    mov ebx, 1
    mov ecx, space
    mov edx, 1
    int 0x80
    jmp .continue_col

.print_border_char:
    mov eax, 4
    mov ebx, 1
    mov ecx, hash
    mov edx, 1
    int 0x80
    jmp .continue_col

.draw_dotted:
    mov eax, 4
    mov ebx, 1
    mov ecx, dot
    mov edx, 1
    int 0x80

.continue_col:
    pop edi
    pop ecx
    inc edi
    dec ecx
    jnz .col_loop

    ; Print newline
    mov eax, 4
    mov ebx, 1
    mov ecx, newline
    mov edx, 1
    int 0x80

    pop esi
    pop ecx
    inc esi
    dec ecx
    jnz .row_loop

    mov edi, color_reset
    call print_string

    pop esi
    pop edx
    pop ecx
    pop ebx
    ret

; Draw triangle with different styles - 重构版本避免无限循环
draw_triangle:
    push ebx
    push ecx
    push edx
    push esi
    push edi

    ; 显示颜色
    mov edi, color_cyan
    call print_string

    ; 获取样式和大小
    mov ebx, [style]                        ; 样式存储在EBX
    mov esi, [size]                         ; 大小存储在ESI
    mov ecx, 1                              ; 当前行号，从1开始

.row_loop:
    ; 检查是否完成所有行
    cmp ecx, esi
    jg .triangle_done

    ; 根据样式选择绘制方法
    cmp ebx, 1
    je .draw_right_aligned
    cmp ebx, 2
    je .draw_centered
    cmp ebx, 3
    je .draw_hollow
    ; 默认使用右对齐
    jmp .draw_right_aligned

.draw_right_aligned:
    ; 右对齐三角形：每行打印 row_number 个星号
    mov edx, ecx                            ; EDX = 当前行要打印的星号数
.right_star_loop:
    cmp edx, 0
    jle .print_newline

    push ecx
    push edx
    mov eax, 4
    mov ebx, 1
    mov ecx, star
    mov edx, 1
    int 0x80
    pop edx
    pop ecx

    dec edx
    jmp .right_star_loop

.draw_centered:
    ; 居中等腰三角形
    ; 计算前导空格数：spaces = size - current_row
    mov edi, esi                            ; EDI = size
    sub edi, ecx                            ; EDI = size - current_row

    ; 打印前导空格
.center_space_loop:
    cmp edi, 0
    jle .center_stars

    push ecx
    push edi
    mov eax, 4
    mov ebx, 1
    mov ecx, space
    mov edx, 1
    int 0x80
    pop edi
    pop ecx

    dec edi
    jmp .center_space_loop

.center_stars:
    ; 打印星号：每行打印 (2 * row_number - 1) 个星号形成等腰三角形
    mov edx, ecx                            ; EDX = current_row
    add edx, edx                            ; EDX = current_row * 2 (使用add代替shl避免问题)
    dec edx                                 ; EDX = current_row * 2 - 1

    ; 确保至少打印1个星号
    cmp edx, 1
    jge .center_star_loop
    mov edx, 1                              ; 最少1个星号

.center_star_loop:
    cmp edx, 0
    jle .print_newline

    push ecx
    push edx
    mov eax, 4
    mov ebx, 1
    mov ecx, star
    mov edx, 1
    int 0x80
    pop edx
    pop ecx

    dec edx
    jmp .center_star_loop

.draw_hollow:
    ; 空心等腰三角形
    ; 计算并打印前导空格
    mov edi, esi                            ; EDI = size
    sub edi, ecx                            ; EDI = size - current_row
.hollow_space_loop:
    cmp edi, 0
    jle .hollow_stars

    push ecx
    push edi
    mov eax, 4
    mov ebx, 1
    mov ecx, space
    mov edx, 1
    int 0x80
    pop edi
    pop ecx

    dec edi
    jmp .hollow_space_loop

.hollow_stars:
    ; 第一行：只有一个井号
    cmp ecx, 1
    je .hollow_single_char
    ; 最后一行：填充整行
    cmp ecx, esi
    je .hollow_fill

    ; 中间行：只画左右边框
    ; 左边框
    push ecx
    mov eax, 4
    mov ebx, 1
    mov ecx, hash
    mov edx, 1
    int 0x80
    pop ecx

    ; 计算中间空格数：(2 * row_number - 3) 个空格
    mov edx, ecx                            ; EDX = current_row
    add edx, edx                            ; EDX = current_row * 2
    sub edx, 3                              ; EDX = current_row * 2 - 3

    ; 打印中间空格
.hollow_middle_loop:
    cmp edx, 0
    jle .hollow_right_border
    push ecx
    push edx
    mov eax, 4
    mov ebx, 1
    mov ecx, space
    mov edx, 1
    int 0x80
    pop edx
    pop ecx
    dec edx
    jmp .hollow_middle_loop

.hollow_right_border:
    ; 右边框
    push ecx
    mov eax, 4
    mov ebx, 1
    mov ecx, hash
    mov edx, 1
    int 0x80
    pop ecx
    jmp .print_newline

.hollow_single_char:
    ; 第一行：只打印一个井号
    push ecx
    mov eax, 4
    mov ebx, 1
    mov ecx, hash
    mov edx, 1
    int 0x80
    pop ecx
    jmp .print_newline

.hollow_fill:
    ; 最后一行：填充 (2 * row_number - 1) 个井号
    mov edx, ecx                            ; EDX = current_row
    add edx, edx                            ; EDX = current_row * 2
    dec edx                                 ; EDX = current_row * 2 - 1

    ; 确保至少1个字符
    cmp edx, 1
    jge .hollow_fill_loop
    mov edx, 1

.hollow_fill_loop:
    cmp edx, 0
    jle .print_newline

    push ecx
    push edx
    mov eax, 4
    mov ebx, 1
    mov ecx, hash
    mov edx, 1
    int 0x80
    pop edx
    pop ecx

    dec edx
    jmp .hollow_fill_loop

.print_newline:
    ; 打印换行符
    push ecx
    mov eax, 4
    mov ebx, 1
    mov ecx, newline
    mov edx, 1
    int 0x80
    pop ecx

    ; 下一行
    inc ecx
    jmp .row_loop

.triangle_done:
    ; 重置颜色
    mov edi, color_reset
    call print_string

    pop edi
    pop esi
    pop edx
    pop ecx
    pop ebx
    ret

; Draw square with different styles
draw_square:
    push ebx
    push ecx
    push edx
    push esi

    mov edi, color_green
    call print_string

    mov esi, 1                              ; Row counter
    mov ecx, [size]
.row_loop:
    push ecx
    push esi
    mov ecx, [size]
    mov edi, 1                              ; Column counter

.col_loop:
    push ecx
    push edi

    ; Choose character based on style
    mov eax, [style]
    cmp eax, 1
    je .draw_filled
    cmp eax, 2
    je .draw_border
    cmp eax, 3
    je .draw_dotted
    jmp .draw_filled

.draw_filled:
    mov eax, 4
    mov ebx, 1
    mov ecx, star
    mov edx, 1
    int 0x80
    jmp .continue_col

.draw_border:
    ; Check if on border
    cmp esi, 1                              ; First row?
    je .print_border_char
    cmp esi, [size]                         ; Last row?
    je .print_border_char
    cmp edi, 1                              ; First column?
    je .print_border_char
    cmp edi, [size]                         ; Last column?
    je .print_border_char

    ; Interior - print space
    mov eax, 4
    mov ebx, 1
    mov ecx, space
    mov edx, 1
    int 0x80
    jmp .continue_col

.print_border_char:
    mov eax, 4
    mov ebx, 1
    mov ecx, hash
    mov edx, 1
    int 0x80
    jmp .continue_col

.draw_dotted:
    mov eax, 4
    mov ebx, 1
    mov ecx, dot
    mov edx, 1
    int 0x80

.continue_col:
    pop edi
    pop ecx
    inc edi
    dec ecx
    jnz .col_loop

    ; Print newline
    mov eax, 4
    mov ebx, 1
    mov ecx, newline
    mov edx, 1
    int 0x80

    pop esi
    pop ecx
    inc esi
    dec ecx
    jnz .row_loop

    mov edi, color_reset
    call print_string

    pop esi
    pop edx
    pop ecx
    pop ebx
    ret

; Draw circle using ellipse formula
draw_circle:
    push ebx
    push ecx
    push edx
    push esi
    push edi

    mov edi, color_yellow
    call print_string

    mov ebp, -15                            ; y coordinate from -15 to 15

y_loop:
    mov edi, -30                            ; x coordinate from -30 to 30

x_loop:
    ; Calculate x^2
    mov eax, edi
    imul eax, eax

    ; Calculate (2y)^2
    mov ebx, ebp
    imul ebx, 2
    imul ebx, ebx

    add eax, ebx                            ; x^2 + (2y)^2

    cmp eax, 900                            ; Compare with radius^2 (30^2)
    jg .print_space

.print_star:
    mov eax, 4
    mov ebx, 1
    mov ecx, star
    mov edx, 1
    int 0x80
    jmp .continue_x_loop

.print_space:
    mov eax, 4
    mov ebx, 1
    mov ecx, space
    mov edx, 1
    int 0x80

.continue_x_loop:
    inc edi
    cmp edi, 30
    jle x_loop

    ; Print newline at end of row
    mov eax, 4
    mov ebx, 1
    mov ecx, newline
    mov edx, 1
    int 0x80

    inc ebp
    cmp ebp, 15
    jle y_loop

    mov edi, color_reset
    call print_string

    pop edi
    pop esi
    pop edx
    pop ecx
    pop ebx
    ret

; --- Utility functions ---

; Print string (null-terminated)
print_string:
    push ebx
    push ecx
    push edx

    mov ecx, edi
    mov edx, 0
.strlen:
    cmp byte [ecx], 0
    je .print
    inc ecx
    inc edx
    jmp .strlen
.print:
    mov eax, 4
    mov ebx, 1
    mov ecx, edi
    int 0x80

    pop edx
    pop ecx
    pop ebx
    ret

; Read integer from input
read_int:
    push ebx
    push ecx
    push edx
    push esi

    ; Clear input buffer
    mov edi, input
    mov ecx, 16
    xor al, al
    rep stosb

    ; Read input
    mov eax, 3
    mov ebx, 0
    mov ecx, input
    mov edx, 16
    int 0x80

    ; Check if read failed
    cmp eax, 0
    jle .invalid

    ; Check for empty input
    cmp eax, 1
    je .invalid

    ; Replace newline with null terminator
    dec eax
    mov byte [input + eax], 0

    ; Convert string to integer
    xor eax, eax
    xor ecx, ecx

.convert_loop:
    movzx ebx, byte [input + ecx]
    test bl, bl
    jz .done

    ; Check if character is a digit
    cmp bl, '0'
    jb .invalid
    cmp bl, '9'
    ja .invalid

    ; Convert ASCII to digit and add to result
    sub bl, '0'
    imul eax, 10
    add eax, ebx

    inc ecx
    jmp .convert_loop

.invalid:
    mov eax, -1

.done:
    pop esi
    pop edx
    pop ecx
    pop ebx
    ret

exit_program:
    ; Print goodbye message
    mov edi, color_green
    call print_string
    mov edi, msg_goodbye
    call print_string
    mov edi, color_reset
    call print_string

    mov eax, 1
    xor ebx, ebx
    int 0x80
